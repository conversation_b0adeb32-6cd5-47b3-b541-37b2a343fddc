# Cloudinary Setup for Thrifta

## Step 1: Create Cloudinary Account

1. Go to [Cloudinary](https://cloudinary.com/)
2. Click "Sign up for free"
3. Fill in your details
4. Verify your email
5. Complete the onboarding

## Step 2: Get Your Credentials

1. Go to your **Dashboard**
2. You'll see your account details:
   - **Cloud Name**: `your-cloud-name`
   - **API Key**: `***************`
   - **API Secret**: `abcdefghijklmnopqrstuvwxyz` (click to reveal)

## Step 3: Create Upload Preset

1. Go to **Settings** (gear icon)
2. Click **Upload** tab
3. Scroll to **Upload presets**
4. Click **Add upload preset**

### Configure the preset:
- **Preset name**: `thrifta-shops`
- **Signing Mode**: **Unsigned** (important!)
- **Folder**: `thrifta/shops`
- **Format**: Auto
- **Quality**: Auto
- **Transformation**: 
  - Width: 1200
  - Height: 800
  - Crop: Fill
- Click **Save**

## Step 4: Configure Image Transformations

### For shop thumbnails:
- Create another preset: `thrifta-thumbnails`
- Width: 400
- Height: 300
- Crop: Fill
- Quality: Auto

### For profile images:
- Create preset: `thrifta-profiles`
- Width: 200
- Height: 200
- Crop: Fill
- Gravity: Face (for profile photos)

## Step 5: Test Upload

You can test your setup by uploading an image:
1. Go to **Media Library**
2. Click **Upload**
3. Select an image
4. Check if it appears in your library

## Your Credentials

After setup, you'll have:
- **Cloud Name**: `your-cloud-name`
- **API Key**: `***************`
- **API Secret**: `your-secret-key`
- **Upload Preset**: `thrifta-shops`

## Next Steps

1. Copy these credentials
2. Add them to your `.env.local` file
3. Test image uploads in your app

Your Cloudinary is ready for Thrifta!
