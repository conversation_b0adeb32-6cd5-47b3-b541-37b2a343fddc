'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CheckCircle, ArrowRight, Home, Eye } from 'lucide-react';
import { getShop, Shop } from '@/lib/database';

export default function ShopCreatedSuccessPage() {
  const [shop, setShop] = useState<Shop | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const shopId = searchParams.get('shopId');

  useEffect(() => {
    if (shopId) {
      loadShop();
    } else {
      setLoading(false);
    }
  }, [shopId]);

  const loadShop = async () => {
    try {
      if (shopId) {
        const shopData = await getShop(shopId);
        setShop(shopData);
      }
    } catch (error) {
      console.error('Error loading shop:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[var(--color-primary)]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Success Content */}
      <div className="container-wide container-padding py-20">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="mb-12">
            <CheckCircle className="h-24 w-24 text-green-500 mx-auto mb-8" />
            <h1 className="text-display text-5xl font-bold text-[var(--color-text-primary)] mb-6">
              Shop Created Successfully!
            </h1>
            <p className="text-body text-xl text-[var(--color-text-secondary)] leading-relaxed">
              {shop ? `${shop.name} has been created and is now live on Thrifta.` : 'Your shop has been created and is now live on Thrifta.'}
            </p>
          </div>

          {/* Shop Preview */}
          {shop && (
            <div className="card mb-12 text-left">
              <div className="relative h-64 overflow-hidden">
                <img
                  src={shop.mainImage}
                  alt={shop.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-10">
                <h3 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-4">
                  {shop.name}
                </h3>
                <p className="text-body text-[var(--color-text-secondary)] mb-6 leading-relaxed">
                  {shop.description}
                </p>
                <div className="flex items-center text-[var(--color-text-light)]">
                  <span className="text-sm">{shop.location.city}</span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            {shop && (
              <Link 
                href={`/shop/${shop.id}`}
                className="btn btn-primary flex items-center justify-center space-x-2"
              >
                <Eye className="h-5 w-5" />
                <span>View Your Shop</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
            )}
            
            <Link 
              href="/"
              className="btn btn-secondary flex items-center justify-center space-x-2"
            >
              <Home className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
          </div>

          {/* Next Steps */}
          <div className="mt-16 p-8 bg-[var(--color-surface)] rounded-2xl text-left">
            <h3 className="text-heading text-xl font-bold text-[var(--color-text-primary)] mb-6">
              What's Next?
            </h3>
            <ul className="space-y-4 text-[var(--color-text-secondary)]">
              <li className="flex items-start">
                <div className="w-2 h-2 bg-[var(--color-primary)] rounded-full mt-2 mr-4 flex-shrink-0"></div>
                <span>Share your shop with friends and on social media to get your first followers</span>
              </li>
              <li className="flex items-start">
                <div className="w-2 h-2 bg-[var(--color-primary)] rounded-full mt-2 mr-4 flex-shrink-0"></div>
                <span>Keep your shop updated with new photos and inventory</span>
              </li>
              <li className="flex items-start">
                <div className="w-2 h-2 bg-[var(--color-primary)] rounded-full mt-2 mr-4 flex-shrink-0"></div>
                <span>Engage with the Thrifta community by following other shops</span>
              </li>
              <li className="flex items-start">
                <div className="w-2 h-2 bg-[var(--color-primary)] rounded-full mt-2 mr-4 flex-shrink-0"></div>
                <span>Update your contact information to help customers reach you</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
