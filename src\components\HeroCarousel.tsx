'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Shop {
  id: string;
  name: string;
  description: string;
  image: string;
  categories: string[];
}

interface HeroCarouselProps {
  shops: Shop[];
}

export default function HeroCarousel({ shops }: HeroCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === shops.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(timer);
  }, [shops.length]);

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? shops.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === shops.length - 1 ? 0 : currentIndex + 1);
  };

  if (!shops.length) return null;

  const currentShop = shops[currentIndex];

  return (
    <div className="relative h-96 md:h-[500px] overflow-hidden rounded-2xl shadow-lifted">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center transition-all duration-700 ease-in-out"
        style={{ 
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${currentShop.image})` 
        }}
      />
      
      {/* Content Overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center text-white px-6 max-w-4xl">
          <h1 className="font-serif text-4xl md:text-6xl font-bold mb-4">
            {currentShop.name}
          </h1>
          <p className="text-lg md:text-xl mb-6 opacity-90 max-w-2xl mx-auto">
            {currentShop.description}
          </p>
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {currentShop.categories.map((category, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium backdrop-blur-sm"
              >
                {category}
              </span>
            ))}
          </div>
          <button className="btn-vintage btn-primary text-lg px-8 py-4">
            Explore Shop
          </button>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={goToPrevious}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all duration-200 backdrop-blur-sm"
      >
        <ChevronLeft className="h-6 w-6 text-white" />
      </button>
      <button
        onClick={goToNext}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all duration-200 backdrop-blur-sm"
      >
        <ChevronRight className="h-6 w-6 text-white" />
      </button>

      {/* Dots Indicator */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {shops.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              index === currentIndex 
                ? 'bg-white' 
                : 'bg-white bg-opacity-50 hover:bg-opacity-75'
            }`}
          />
        ))}
      </div>
    </div>
  );
}
