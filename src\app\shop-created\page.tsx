'use client';

import Link from 'next/link';
import { CheckCircle, <PERSON>R<PERSON>, Share2 } from 'lucide-react';

export default function ShopCreated() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-[var(--color-surface)] flex items-center justify-center">
      <div className="max-w-2xl mx-auto container-padding text-center">
        {/* Success Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          
          {/* Confetti Effect */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
              <div className="w-2 h-2 bg-secondary rounded-full animate-bounce ml-4" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-accent rounded-full animate-bounce ml-4" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>

        {/* Success Message */}
        <h1 className="text-display text-5xl font-bold text-[var(--color-text-primary)] mb-6">
          Congratulations!
        </h1>
        <p className="text-body text-xl text-[var(--color-text-secondary)] mb-8 leading-relaxed">
          Your shop has been successfully created and is now part of the Thrifta community. 
          Vintage enthusiasts can now discover your curated collection.
        </p>

        {/* Shop Preview Card */}
        <div className="card p-8 mb-12 text-left">
          <div className="flex items-start space-x-6">
            <div className="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl font-bold">M</span>
            </div>
            <div className="flex-1">
              <h3 className="text-heading text-xl font-bold text-[var(--color-text-primary)] mb-2">
                Your Shop is Live!
              </h3>
              <p className="text-body text-[var(--color-text-secondary)] mb-4">
                Your vintage treasures are now discoverable by collectors worldwide.
              </p>
              <div className="flex items-center space-x-4 text-sm text-[var(--color-text-light)]">
                <span>✨ Featured in search</span>
                <span>📍 Location verified</span>
                <span>💎 Premium listing</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <Link href="/shop/1" className="btn btn-primary flex items-center justify-center space-x-2">
            <span>View My Shop</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
          <button className="btn btn-secondary flex items-center justify-center space-x-2">
            <Share2 className="h-4 w-4" />
            <span>Share Shop</span>
          </button>
        </div>

        {/* Next Steps */}
        <div className="text-left">
          <h2 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-6 text-center">
            What's Next?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary font-bold">1</span>
              </div>
              <h3 className="text-heading font-bold text-[var(--color-text-primary)] mb-2">
                Add More Photos
              </h3>
              <p className="text-body text-sm text-[var(--color-text-secondary)]">
                Upload additional photos to showcase your collection
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary font-bold">2</span>
              </div>
              <h3 className="text-heading font-bold text-[var(--color-text-primary)] mb-2">
                Connect with Collectors
              </h3>
              <p className="text-body text-sm text-[var(--color-text-secondary)]">
                Build relationships with vintage enthusiasts
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary font-bold">3</span>
              </div>
              <h3 className="text-heading font-bold text-[var(--color-text-primary)] mb-2">
                Share Your Story
              </h3>
              <p className="text-body text-sm text-[var(--color-text-secondary)]">
                Tell collectors about your unique finds
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-[var(--color-border)]">
          <p className="text-body text-[var(--color-text-light)]">
            Welcome to the Thrifta community! 
            <Link href="/" className="text-primary hover:underline ml-1">
              Explore other shops
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
