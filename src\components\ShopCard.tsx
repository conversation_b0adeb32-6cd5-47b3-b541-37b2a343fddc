'use client';

import Link from 'next/link';
import { MapPin } from 'lucide-react';

interface Shop {
  id: string;
  name: string;
  description: string;
  image: string;
  categories: string[];
  location?: string;
}

interface ShopCardProps {
  shop: Shop;
}

export default function ShopCard({ shop }: ShopCardProps) {
  return (
    <Link href={`/shop/${shop.id}`}>
      <div className="card-vintage group cursor-pointer">
        {/* Shop Image */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={shop.image}
            alt={shop.name}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Shop Info */}
        <div className="p-6">
          <h3 className="font-serif text-xl font-bold text-[var(--color-text-dark)] mb-2 group-hover:text-[var(--color-primary)] transition-colors duration-200">
            {shop.name}
          </h3>
          
          {/* Categories */}
          <div className="flex flex-wrap gap-2 mb-3">
            {shop.categories.slice(0, 3).map((category, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-[var(--color-accent-sage)] bg-opacity-20 text-[var(--color-text-dark)] text-xs font-medium rounded-full"
              >
                {category}
              </span>
            ))}
            {shop.categories.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-[var(--color-text-light)] text-xs font-medium rounded-full">
                +{shop.categories.length - 3} more
              </span>
            )}
          </div>

          {/* Description */}
          <p className="text-[var(--color-text-light)] text-sm mb-4 line-clamp-2">
            {shop.description}
          </p>

          {/* Location */}
          {shop.location && (
            <div className="flex items-center text-[var(--color-text-light)] text-sm">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{shop.location}</span>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}
