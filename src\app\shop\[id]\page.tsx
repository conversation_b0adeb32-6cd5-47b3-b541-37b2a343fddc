'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Heart, MapPin, Camera, ArrowLeft, Share2 } from 'lucide-react';

// Mock shop data
const shopData = {
  id: '1',
  name: '<PERSON><PERSON> Vintage',
  description: 'Curated European antiques and timeless treasures from the finest estates. Each piece in our collection has been carefully selected for its historical significance and exceptional craftsmanship.',
  story: 'Founded in 1987 by <PERSON>, Maison Vintage began as a small collection of family heirlooms. Over three decades, it has grown into one of the most respected vintage shops in the city, known for its impeccable taste and authentic pieces.',
  location: 'Soho District',
  address: '123 Vintage Lane, Soho District',
  contact: '+1 (555) 123-4567',
  email: '<EMAIL>',
  followers: 1247,
  isFollowing: false,
  mainImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
  gallery: [
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ]
};

export default function ShopProfile() {
  const [isFollowing, setIsFollowing] = useState(shopData.isFollowing);
  const [selectedImage, setSelectedImage] = useState(shopData.mainImage);

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // TODO: Implement follow/unfollow API call
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Sharing shop');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-[var(--color-border)] sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="max-w-7xl mx-auto container-padding py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <ArrowLeft className="h-5 w-5 text-[var(--color-text-secondary)]" />
              <span className="text-[var(--color-text-secondary)] hover:text-primary transition-colors">
                Back to shops
              </span>
            </Link>
            <Link href="/" className="text-display text-2xl font-bold text-primary">
              Thrifta
            </Link>
          </div>
        </div>
      </header>

      {/* Main Image Section */}
      <section className="relative">
        <div className="h-[70vh] overflow-hidden">
          <img
            src={selectedImage}
            alt={shopData.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
        </div>
        
        {/* Floating Action Buttons */}
        <div className="absolute top-8 right-8 flex space-x-3">
          <button
            onClick={handleShare}
            className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-soft hover:shadow-medium transition-all"
          >
            <Share2 className="h-5 w-5 text-[var(--color-text-secondary)]" />
          </button>
          <button
            onClick={handleFollow}
            className={`p-3 rounded-full shadow-soft hover:shadow-medium transition-all ${
              isFollowing 
                ? 'bg-primary text-white' 
                : 'bg-white/90 backdrop-blur-sm text-[var(--color-text-secondary)]'
            }`}
          >
            <Heart className={`h-5 w-5 ${isFollowing ? 'fill-current' : ''}`} />
          </button>
        </div>

        {/* Shop Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-7xl mx-auto container-padding">
            <h1 className="text-display text-5xl font-bold mb-4">
              {shopData.name}
            </h1>
            <div className="flex items-center space-x-6 text-lg opacity-90">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                {shopData.location}
              </div>
              <div className="flex items-center">
                <Heart className="h-5 w-5 mr-2" />
                {shopData.followers} followers
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Thumbnails */}
      <section className="py-8 bg-[var(--color-surface)]">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="flex space-x-4 overflow-x-auto pb-4">
            <button
              onClick={() => setSelectedImage(shopData.mainImage)}
              className={`flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden border-2 transition-all ${
                selectedImage === shopData.mainImage 
                  ? 'border-primary shadow-medium' 
                  : 'border-transparent hover:border-[var(--color-border)]'
              }`}
            >
              <img
                src={shopData.mainImage}
                alt="Main"
                className="w-full h-full object-cover"
              />
            </button>
            {shopData.gallery.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(image)}
                className={`flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden border-2 transition-all ${
                  selectedImage === image 
                    ? 'border-primary shadow-medium' 
                    : 'border-transparent hover:border-[var(--color-border)]'
                }`}
              >
                <img
                  src={image}
                  alt={`Gallery ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Shop Details */}
      <section className="section-padding">
        <div className="max-w-4xl mx-auto container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h2 className="text-display text-3xl font-bold text-[var(--color-text-primary)] mb-6">
                About This Shop
              </h2>
              <p className="text-body text-lg text-[var(--color-text-secondary)] leading-relaxed mb-8">
                {shopData.description}
              </p>
              
              <h3 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-4">
                Our Story
              </h3>
              <p className="text-body text-[var(--color-text-secondary)] leading-relaxed">
                {shopData.story}
              </p>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="card p-8 sticky top-32">
                <h3 className="text-heading text-xl font-bold text-[var(--color-text-primary)] mb-6">
                  Visit Us
                </h3>
                
                <div className="space-y-4 mb-8">
                  <div>
                    <p className="text-caption text-[var(--color-text-light)] mb-1">Address</p>
                    <p className="text-body text-[var(--color-text-secondary)]">{shopData.address}</p>
                  </div>
                  <div>
                    <p className="text-caption text-[var(--color-text-light)] mb-1">Phone</p>
                    <p className="text-body text-[var(--color-text-secondary)]">{shopData.contact}</p>
                  </div>
                  <div>
                    <p className="text-caption text-[var(--color-text-light)] mb-1">Email</p>
                    <p className="text-body text-[var(--color-text-secondary)]">{shopData.email}</p>
                  </div>
                </div>

                <button
                  onClick={handleFollow}
                  className={`btn w-full mb-4 ${
                    isFollowing ? 'btn-secondary' : 'btn-primary'
                  }`}
                >
                  {isFollowing ? 'Following' : 'Follow Shop'}
                </button>

                <button className="btn btn-secondary w-full flex items-center justify-center space-x-2">
                  <Camera className="h-4 w-4" />
                  <span>Add Photo</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
