# Thrifta Setup Guide

## Overview
Thrifta is a premium platform for discovering thrift shops and antique stores. This guide covers the complete setup process including database, authentication, file storage, and deployment.

## Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Database**: Firebase Firestore (NoSQL, real-time)
- **Authentication**: Firebase Auth (Google, Email/Password)
- **File Storage**: Cloudinary (image optimization, CDN)
- **Hosting**: Vercel (seamless Next.js deployment)

## 1. Firebase Setup

### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Name it "thrifta-app"
4. Enable Google Analytics (optional)

### Enable Authentication
1. Go to Authentication > Sign-in method
2. Enable Email/Password
3. Enable Google (add your domain)
4. Configure OAuth consent screen

### Setup Firestore Database
1. Go to Firestore Database
2. Create database in production mode
3. Choose location closest to your users
4. Set up security rules (see below)

### Get Firebase Config
1. Go to Project Settings > General
2. Scroll to "Your apps" section
3. Click "Web" icon to add web app
4. Copy the config object

## 2. Cloudinary Setup

### Create Account
1. Go to [Cloudinary](https://cloudinary.com/)
2. Sign up for free account
3. Go to Dashboard to get credentials

### Configuration
- **Cloud Name**: Your unique cloud name
- **API Key**: For server-side operations
- **API Secret**: Keep this secure
- **Upload Preset**: Create unsigned preset for client uploads

## 3. Environment Variables

Create `.env.local` file in your project root:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 4. Database Schema

### Collections Structure

#### shops
```javascript
{
  id: "auto-generated",
  name: "Shop Name",
  description: "Shop description",
  story: "Shop story/background",
  ownerId: "user_id",
  images: ["cloudinary_url1", "cloudinary_url2"],
  mainImage: "cloudinary_url",
  location: {
    address: "Full address",
    city: "City",
    coordinates: { lat: 0, lng: 0 }
  },
  contact: {
    phone: "+1234567890",
    email: "<EMAIL>"
  },
  followers: ["user_id1", "user_id2"],
  followerCount: 0,
  verified: false,
  featured: false,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

#### users
```javascript
{
  id: "firebase_auth_uid",
  email: "<EMAIL>",
  displayName: "User Name",
  photoURL: "profile_image_url",
  following: ["shop_id1", "shop_id2"],
  ownedShops: ["shop_id1"],
  createdAt: timestamp,
  lastLoginAt: timestamp
}
```

#### photos
```javascript
{
  id: "auto-generated",
  shopId: "shop_id",
  uploadedBy: "user_id",
  imageUrl: "cloudinary_url",
  caption: "Photo caption",
  approved: false,
  createdAt: timestamp
}
```

## 5. Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Anyone can read shops, only owners can write
    match /shops/{shopId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.ownerId;
    }
    
    // Photos require authentication to create
    match /photos/{photoId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.uploadedBy;
    }
  }
}
```

## 6. Required Dependencies

Install additional packages:

```bash
npm install firebase
npm install cloudinary
npm install @types/node
npm install react-hot-toast  # For notifications
npm install framer-motion    # For animations
```

## 7. How Users Add Shops

### Process Flow:
1. **Authentication Required**: Users must sign in
2. **Navigate to Create Shop**: Click "Add Your Shop" in header
3. **Fill Form**: Complete shop details and upload photos
4. **Photo Upload**: Images uploaded to Cloudinary
5. **Save to Database**: Shop data saved to Firestore
6. **Verification**: Admin can verify shops (optional)
7. **Go Live**: Shop appears in listings

### Features:
- **Drag & Drop Photos**: Easy image upload
- **Real-time Preview**: See shop as you build it
- **Auto-save**: Form data preserved
- **Validation**: Required fields and image limits
- **Success Page**: Confirmation and next steps

## 8. Key Features to Implement

### Phase 1 (MVP):
- [x] User authentication (Firebase Auth)
- [x] Shop creation form
- [x] Shop listings and search
- [x] Shop profile pages
- [x] Follow/unfollow functionality
- [ ] Photo upload to Cloudinary
- [ ] Firebase integration

### Phase 2 (Enhanced):
- [ ] Advanced search and filters
- [ ] User profiles and dashboards
- [ ] Photo approval system
- [ ] Shop verification badges
- [ ] Mobile app (React Native)

## 9. Deployment

### Vercel Deployment:
1. Push code to GitHub
2. Connect Vercel to your repository
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Domain Setup:
1. Purchase domain (e.g., thrifta.com)
2. Configure DNS in Vercel
3. Enable HTTPS (automatic)

## 10. Next Steps

1. **Set up Firebase project** and get credentials
2. **Create Cloudinary account** and configure upload preset
3. **Add environment variables** to your project
4. **Install Firebase SDK** and implement authentication
5. **Implement Cloudinary upload** in shop creation form
6. **Test the complete flow** from signup to shop creation
7. **Deploy to Vercel** for production

## Support

For questions or issues:
- Check Firebase documentation
- Review Cloudinary guides
- Test locally before deploying
- Monitor console for errors

This setup provides a scalable, production-ready foundation for Thrifta!
