import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  arrayUnion, 
  arrayRemove,
  query,
  where,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from './firebase';

export interface Shop {
  id: string;
  name: string;
  description: string;
  story?: string;
  ownerId: string;
  images: string[];
  mainImage: string;
  location: {
    address: string;
    city: string;
    coordinates?: { lat: number; lng: number };
  };
  contact: {
    phone?: string;
    email: string;
  };
  followers: string[];
  followerCount: number;
  verified: boolean;
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL: string;
  following: string[];
  ownedShops: string[];
  createdAt: Date;
  lastLoginAt: Date;
}

// Shop operations
export const createShop = async (shopData: Omit<Shop, 'id' | 'followers' | 'followerCount' | 'verified' | 'featured' | 'createdAt' | 'updatedAt'>) => {
  try {
    const shopRef = doc(collection(db, 'shops'));
    const shop: Shop = {
      ...shopData,
      id: shopRef.id,
      followers: [],
      followerCount: 0,
      verified: false,
      featured: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await setDoc(shopRef, shop);

    // Add shop to user's owned shops
    const userRef = doc(db, 'users', shopData.ownerId);
    await updateDoc(userRef, {
      ownedShops: arrayUnion(shopRef.id)
    });

    return shop;
  } catch (error) {
    console.error('Error creating shop:', error);
    throw error;
  }
};

export const getShop = async (shopId: string): Promise<Shop | null> => {
  try {
    const shopRef = doc(db, 'shops', shopId);
    const shopSnap = await getDoc(shopRef);
    
    if (shopSnap.exists()) {
      return { id: shopSnap.id, ...shopSnap.data() } as Shop;
    }
    return null;
  } catch (error) {
    console.error('Error getting shop:', error);
    throw error;
  }
};

export const getAllShops = async (): Promise<Shop[]> => {
  try {
    const shopsRef = collection(db, 'shops');
    const q = query(shopsRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Shop[];
  } catch (error) {
    console.error('Error getting shops:', error);
    throw error;
  }
};

export const searchShops = async (searchTerm: string): Promise<Shop[]> => {
  try {
    const shopsRef = collection(db, 'shops');
    const querySnapshot = await getDocs(shopsRef);
    
    // Client-side filtering (for simple search)
    // For production, consider using Algolia or similar for better search
    const shops = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Shop[];

    return shops.filter(shop => 
      shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shop.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shop.location.city.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('Error searching shops:', error);
    throw error;
  }
};

export const followShop = async (shopId: string, userId: string) => {
  try {
    const shopRef = doc(db, 'shops', shopId);
    const userRef = doc(db, 'users', userId);

    // Add user to shop's followers
    await updateDoc(shopRef, {
      followers: arrayUnion(userId),
      followerCount: arrayUnion(userId).length
    });

    // Add shop to user's following
    await updateDoc(userRef, {
      following: arrayUnion(shopId)
    });

    // Update follower count
    const shopSnap = await getDoc(shopRef);
    if (shopSnap.exists()) {
      const shop = shopSnap.data() as Shop;
      await updateDoc(shopRef, {
        followerCount: shop.followers.length
      });
    }
  } catch (error) {
    console.error('Error following shop:', error);
    throw error;
  }
};

export const unfollowShop = async (shopId: string, userId: string) => {
  try {
    const shopRef = doc(db, 'shops', shopId);
    const userRef = doc(db, 'users', userId);

    // Remove user from shop's followers
    await updateDoc(shopRef, {
      followers: arrayRemove(userId)
    });

    // Remove shop from user's following
    await updateDoc(userRef, {
      following: arrayRemove(shopId)
    });

    // Update follower count
    const shopSnap = await getDoc(shopRef);
    if (shopSnap.exists()) {
      const shop = shopSnap.data() as Shop;
      await updateDoc(shopRef, {
        followerCount: shop.followers.length
      });
    }
  } catch (error) {
    console.error('Error unfollowing shop:', error);
    throw error;
  }
};

export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return userSnap.data() as UserProfile;
    }
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};
