# Firebase Setup for Thrifta

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Project name: `thrifta-app`
4. Enable Google Analytics (optional)
5. Choose your Google Analytics account
6. Click "Create project"

## Step 2: Enable Authentication

1. In Firebase Console, go to **Authentication**
2. Click "Get started"
3. Go to **Sign-in method** tab
4. Enable these providers:
   - **Email/Password** (click Enable)
   - **Google** (click Enable, add your domain)

### Configure Google Sign-in:
- Add authorized domains: `localhost`, `your-domain.com`
- Download the config (we'll use it next)

## Step 3: Create Firestore Database

1. Go to **Firestore Database**
2. Click "Create database"
3. Choose **"Start in production mode"**
4. Select location closest to your users
5. Click "Done"

## Step 4: Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll to "Your apps" section
3. Click the **Web** icon `</>`
4. App nickname: `thrifta-web`
5. **Copy the config object** - you'll need this!

```javascript
// Your config will look like this:
const firebaseConfig = {
  apiKey: "AIza...",
  authDomain: "thrifta-app.firebaseapp.com",
  projectId: "thrifta-app",
  storageBucket: "thrifta-app.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abc123"
};
```

## Step 5: Set Firestore Security Rules

1. Go to **Firestore Database** > **Rules**
2. Replace the rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Anyone can read shops, only owners can write
    match /shops/{shopId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.ownerId;
    }
    
    // Photos require authentication to create
    match /photos/{photoId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.uploadedBy;
    }
  }
}
```

3. Click **Publish**

## Next Steps

After completing these steps:
1. Copy your Firebase config
2. Set up Cloudinary account
3. Add environment variables
4. Test the integration

Your Firebase project will be ready for Thrifta!
