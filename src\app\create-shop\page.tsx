'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Upload, X, MapPin, Phone, Mail } from 'lucide-react';

interface ShopFormData {
  name: string;
  description: string;
  story: string;
  address: string;
  phone: string;
  email: string;
  photos: File[];
}

export default function CreateShop() {
  const [formData, setFormData] = useState<ShopFormData>({
    name: '',
    description: '',
    story: '',
    address: '',
    phone: '',
    email: '',
    photos: []
  });
  const [dragActive, setDragActive] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    );
    
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, ...files].slice(0, 10) // Max 10 photos
    }));
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, ...files].slice(0, 10)
    }));
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // TODO: Implement shop creation API call
    console.log('Creating shop:', formData);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to success page or shop profile
      window.location.href = '/shop-created';
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-[var(--color-border)] sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="max-w-7xl mx-auto container-padding py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <ArrowLeft className="h-5 w-5 text-[var(--color-text-secondary)]" />
              <span className="text-[var(--color-text-secondary)] hover:text-primary transition-colors">
                Back to home
              </span>
            </Link>
            <Link href="/" className="text-display text-2xl font-bold text-primary">
              Thrifta
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-white to-[var(--color-surface)]">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h1 className="text-display text-5xl font-bold text-[var(--color-text-primary)] mb-6">
            Create Your Shop
          </h1>
          <p className="text-body text-xl text-[var(--color-text-secondary)] mb-12 max-w-2xl mx-auto">
            Share your curated collection with fellow vintage enthusiasts and build your community of collectors.
          </p>
        </div>
      </section>

      {/* Form Section */}
      <section className="section-padding">
        <div className="max-w-4xl mx-auto container-padding">
          <form onSubmit={handleSubmit} className="space-y-12">
            {/* Photo Upload */}
            <div>
              <h2 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-6">
                Shop Photos
              </h2>
              <div
                className={`border-2 border-dashed rounded-2xl p-12 text-center transition-all ${
                  dragActive 
                    ? 'border-primary bg-primary/5' 
                    : 'border-[var(--color-border)] hover:border-primary/50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 text-[var(--color-text-light)] mx-auto mb-4" />
                <h3 className="text-heading text-xl font-bold text-[var(--color-text-primary)] mb-2">
                  Upload Shop Photos
                </h3>
                <p className="text-body text-[var(--color-text-secondary)] mb-6">
                  Drag and drop your photos here, or click to browse
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="photo-upload"
                />
                <label htmlFor="photo-upload" className="btn btn-secondary">
                  Choose Photos
                </label>
              </div>

              {/* Photo Preview */}
              {formData.photos.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  {formData.photos.map((photo, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Shop Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-6">
                  Shop Details
                </h2>
                <div className="space-y-6">
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Shop Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Enter your shop name"
                      required
                    />
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="input resize-none"
                      placeholder="Describe what makes your shop special"
                      required
                    />
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Your Story
                    </label>
                    <textarea
                      name="story"
                      value={formData.story}
                      onChange={handleInputChange}
                      rows={6}
                      className="input resize-none"
                      placeholder="Tell us about your journey and passion for vintage collecting"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-6">
                  Contact Information
                </h2>
                <div className="space-y-6">
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Address
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-5 w-5" />
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className="input pl-12"
                        placeholder="Shop address"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Phone Number
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-5 w-5" />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="input pl-12"
                        placeholder="Contact number"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-2 block">
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-5 w-5" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input pl-12"
                        placeholder="Contact email"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="text-center pt-8">
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn btn-primary text-lg px-12 py-4 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating Your Shop...' : 'Create My Shop'}
              </button>
            </div>
          </form>
        </div>
      </section>
    </div>
  );
}
