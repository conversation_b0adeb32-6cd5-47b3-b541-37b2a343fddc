'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Upload, X, MapPin, Phone, Mail } from 'lucide-react';

interface ShopFormData {
  name: string;
  description: string;
  story: string;
  address: string;
  phone: string;
  email: string;
  photos: File[];
}

export default function CreateShop() {
  const [formData, setFormData] = useState<ShopFormData>({
    name: '',
    description: '',
    story: '',
    address: '',
    phone: '',
    email: '',
    photos: []
  });
  const [dragActive, setDragActive] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    );
    
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, ...files].slice(0, 10) // Max 10 photos
    }));
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, ...files].slice(0, 10)
    }));
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // TODO: Implement shop creation API call
    console.log('Creating shop:', formData);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to success page or shop profile
      window.location.href = '/shop-created';
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-[var(--color-border)] sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="container-wide container-padding py-8">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-4">
              <ArrowLeft className="h-6 w-6 text-[var(--color-text-secondary)]" />
              <span className="text-[var(--color-text-secondary)] hover:text-primary transition-colors text-lg">
                Back to home
              </span>
            </Link>
            <Link href="/" className="text-display text-3xl font-bold text-primary">
              Thrifta
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-white to-[var(--color-surface)]">
        <div className="container-wide container-padding text-center">
          <h1 className="text-display text-6xl md:text-7xl font-bold text-[var(--color-text-primary)] mb-8 leading-tight">
            Create Your Thrift Shop
          </h1>
          <p className="text-body text-2xl text-[var(--color-text-secondary)] mb-16 max-w-3xl mx-auto leading-relaxed">
            Share your curated collection with fellow vintage enthusiasts and build your community of thrift and antique lovers.
          </p>
        </div>
      </section>

      {/* Form Section */}
      <section className="py-20">
        <div className="container-wide container-padding">
          <form onSubmit={handleSubmit} className="space-y-20 max-w-6xl mx-auto">
            {/* Photo Upload */}
            <div>
              <h2 className="text-display text-4xl font-bold text-[var(--color-text-primary)] mb-10">
                Shop Photos
              </h2>
              <div
                className={`border-2 border-dashed rounded-3xl p-16 text-center transition-all ${
                  dragActive
                    ? 'border-primary bg-primary/5'
                    : 'border-[var(--color-border)] hover:border-primary/50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-16 w-16 text-[var(--color-text-light)] mx-auto mb-6" />
                <h3 className="text-heading text-2xl font-bold text-[var(--color-text-primary)] mb-4">
                  Upload Shop Photos
                </h3>
                <p className="text-body text-lg text-[var(--color-text-secondary)] mb-8 max-w-md mx-auto leading-relaxed">
                  Drag and drop your photos here, or click to browse. Show off your best thrift finds and antique treasures.
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="photo-upload"
                />
                <label htmlFor="photo-upload" className="btn btn-secondary text-lg px-8 py-4">
                  Choose Photos
                </label>
              </div>

              {/* Photo Preview */}
              {formData.photos.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-10">
                  {formData.photos.map((photo, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-40 object-cover rounded-xl shadow-soft"
                      />
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="absolute -top-3 -right-3 bg-red-500 text-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity shadow-medium"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Shop Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-20">
              <div>
                <h2 className="text-display text-4xl font-bold text-[var(--color-text-primary)] mb-10">
                  Shop Details
                </h2>
                <div className="space-y-8">
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Shop Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="input text-lg py-4"
                      placeholder="Enter your thrift shop name"
                      required
                    />
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={5}
                      className="input resize-none text-lg py-4"
                      placeholder="Describe what makes your thrift shop special - your unique finds, specialties, and what customers can expect"
                      required
                    />
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Your Story
                    </label>
                    <textarea
                      name="story"
                      value={formData.story}
                      onChange={handleInputChange}
                      rows={7}
                      className="input resize-none text-lg py-4"
                      placeholder="Tell us about your journey and passion for thrifting and antique collecting. What inspired you to start this shop?"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-display text-4xl font-bold text-[var(--color-text-primary)] mb-10">
                  Contact Information
                </h2>
                <div className="space-y-8">
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Address
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-6 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-6 w-6" />
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className="input pl-16 text-lg py-4"
                        placeholder="Full shop address"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Phone Number
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-6 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-6 w-6" />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="input pl-16 text-lg py-4"
                        placeholder="Contact number"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-caption text-[var(--color-text-secondary)] mb-4 block">
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-6 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-6 w-6" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input pl-16 text-lg py-4"
                        placeholder="Contact email"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="text-center pt-16">
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn btn-primary text-xl px-16 py-6 disabled:opacity-50 disabled:cursor-not-allowed shadow-medium hover:shadow-strong transition-all"
              >
                {isSubmitting ? 'Creating Your Thrift Shop...' : 'Create My Thrift Shop'}
              </button>
              <p className="text-body text-[var(--color-text-light)] mt-6">
                Your shop will be live immediately after creation
              </p>
            </div>
          </form>
        </div>
      </section>
    </div>
  );
}
