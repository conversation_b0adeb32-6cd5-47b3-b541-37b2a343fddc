@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Thrifta Color Palette */
  --color-primary: #C5A880; /* Muted Ochre */
  --color-accent-rose: #D9A5B3; /* <PERSON> Rose */
  --color-accent-sage: #A9BBA9; /* Sage Green */
  --color-background: #FAF8F5; /* Off-white */
  --color-text-dark: #2E2E2E; /* Deep Charcoal */
  --color-text-light: #6B7280; /* Light gray for secondary text */
  --color-white: #FFFFFF;

  /* Shadows and effects */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lifted: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--color-background);
  color: var(--color-text-dark);
  line-height: 1.6;
}

.font-serif {
  font-family: 'Cormorant Garamond', Georgia, 'Times New Roman', serif;
}

.font-sans {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Custom utility classes */
.text-primary {
  color: var(--color-primary);
}

.text-accent-rose {
  color: var(--color-accent-rose);
}

.text-accent-sage {
  color: var(--color-accent-sage);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-accent-rose {
  background-color: var(--color-accent-rose);
}

.bg-accent-sage {
  background-color: var(--color-accent-sage);
}

.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-lifted {
  box-shadow: var(--shadow-lifted);
}

/* Vintage button styles */
.btn-vintage {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-soft);
}

.btn-vintage:hover {
  box-shadow: var(--shadow-lifted);
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover {
  opacity: 0.9;
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* Card styles */
.card-vintage {
  background-color: var(--color-white);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-soft);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-vintage:hover {
  box-shadow: var(--shadow-lifted);
}

/* Input styles */
.input-vintage {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #E5E7EB;
  background-color: var(--color-white);
  transition: all 0.2s ease;
}

.input-vintage:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(197, 168, 128, 0.1);
}
