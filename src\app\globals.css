@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Premium Thrifta Color Palette */
  --color-primary: #B8860B; /* Elegant Gold */
  --color-secondary: #8B4513; /* <PERSON> */
  --color-accent: #D2B48C; /* Warm Tan */
  --color-background: #FEFEFE; /* Pure White */
  --color-surface: #F8F9FA; /* Light Gray */
  --color-text-primary: #1A1A1A; /* Rich Black */
  --color-text-secondary: #6C757D; /* Muted Gray */
  --color-text-light: #ADB5BD; /* Light Gray */
  --color-border: #E9ECEF; /* Subtle Border */

  /* Premium Shadows */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.16);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  line-height: 1.7;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-serif {
  font-family: 'Cormorant Garamond', Georgia, 'Times New Roman', serif;
}

.font-sans {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Premium Typography */
.text-display {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.text-heading {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.text-body {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  letter-spacing: -0.005em;
}

.text-caption {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

/* Premium Colors */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-accent {
  color: var(--color-accent);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-surface {
  background-color: var(--color-surface);
}

/* Premium Shadows */
.shadow-subtle {
  box-shadow: var(--shadow-subtle);
}

.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

/* Premium Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: white;
  color: var(--color-text-primary);
  border: 1.5px solid var(--color-border);
  box-shadow: var(--shadow-subtle);
}

.btn-secondary:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  box-shadow: var(--shadow-soft);
}

.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
  padding: 0.75rem 1.5rem;
}

.btn-ghost:hover {
  color: var(--color-primary);
  background: rgba(184, 134, 11, 0.05);
}

/* Premium Cards */
.card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-border);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-strong);
  border-color: transparent;
}

/* Premium Inputs */
.input {
  width: 100%;
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  border: 1.5px solid var(--color-border);
  background: white;
  font-family: 'Inter', sans-serif;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  color: var(--color-text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px rgba(184, 134, 11, 0.1);
}

.input::placeholder {
  color: var(--color-text-light);
}

/* Spacing System */
.section-padding {
  padding: 5rem 0;
}

.container-padding {
  padding: 0 2rem;
}

@media (min-width: 768px) {
  .container-padding {
    padding: 0 3rem;
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding: 0 4rem;
  }
}
