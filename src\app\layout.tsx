import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Corm<PERSON><PERSON>_<PERSON>mond } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const cormorant = Cormorant_Garamond({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-cormorant",
});

export const metadata: Metadata = {
  title: "Thrifta - Discover Hidden Vintage Gems",
  description: "Find unique thrift shops, vintage stores, and antique treasures. Connect with curated shops selling cameras, jewelry, clothing, and one-of-a-kind finds.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${cormorant.variable} font-sans antialiased`}
      >
        <div className="min-h-screen bg-[var(--color-background)]">
          {children}
        </div>
      </body>
    </html>
  );
}
