'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Heart, MapPin } from 'lucide-react';

// Premium thrift and antique shop data
const featuredShops = [
  {
    id: '1',
    name: 'Vintage Treasures Thrift',
    description: 'Curated European antiques and timeless thrift finds from estate sales',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
    location: 'Soho District'
  },
  {
    id: '2',
    name: 'The Antique Emporium',
    description: 'Rare antique finds and extraordinary vintage pieces from around the world',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
    location: 'Arts Quarter'
  },
  {
    id: '3',
    name: 'Second Chance Boutique',
    description: 'Handcrafted vintage jewelry and artisan treasures from local thrift finds',
    image: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
    location: 'Historic District'
  }
];

const allShops = [
  {
    id: '1',
    name: 'Vintage Treasures Thrift',
    description: 'Curated European antiques and timeless treasures from estate sales and thrift finds.',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Soho District',
    followers: 1247
  },
  {
    id: '2',
    name: 'The Antique Emporium',
    description: 'Rare antique finds and extraordinary vintage pieces for the discerning collector.',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Arts Quarter',
    followers: 892
  },
  {
    id: '3',
    name: 'Second Chance Boutique',
    description: 'Handcrafted vintage jewelry and artisan treasures discovered through thrifting.',
    image: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Historic District',
    followers: 2156
  },
  {
    id: '4',
    name: 'Retro Revival Antiques',
    description: 'Mid-century modern furniture and design pieces from thrift stores and estate sales.',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Design District',
    followers: 743
  },
  {
    id: '5',
    name: 'Thrifted Threads & Vintage',
    description: 'Vintage textiles and fashion pieces that define timeless elegance, sourced from thrift stores.',
    image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Fashion Quarter',
    followers: 1834
  },
  {
    id: '6',
    name: 'Garden Antiques & Thrift',
    description: 'Weathered garden ornaments and outdoor treasures with character from antique markets.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    location: 'Garden District',
    followers: 567
  }
];

export default function Home() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen">
      {/* Premium Header */}
      <header className="bg-white border-b border-[var(--color-border)] sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="container-wide container-padding py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center">
              <h1 className="text-display text-4xl font-bold text-primary">
                Thrifta
              </h1>
            </Link>
            <nav className="flex items-center space-x-8">
              <Link href="/auth/login" className="btn-ghost">
                Sign In
              </Link>
              <Link href="/auth/signup" className="btn btn-primary">
                Join Thrifta
              </Link>
              <Link href="/create-shop" className="btn btn-secondary">
                Add Your Shop
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-white to-[var(--color-surface)]">
        <div className="container-wide container-padding text-center">
          <h1 className="text-display text-6xl md:text-7xl font-bold text-[var(--color-text-primary)] mb-8 leading-tight">
            Discover Extraordinary
            <br />
            <span className="text-primary">Thrift & Antique Treasures</span>
          </h1>
          <p className="text-body text-xl text-[var(--color-text-secondary)] mb-12 max-w-2xl mx-auto leading-relaxed">
            Connect with curated thrift shops and antique stores. Discover one-of-a-kind vintage pieces that tell stories of the past.
          </p>

          {/* Premium Search */}
          <div className="max-w-2xl mx-auto mb-16">
            <div className="relative">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-[var(--color-text-light)] h-5 w-5" />
              <input
                type="text"
                placeholder="Search for thrift shops, antique stores, or vintage treasures..."
                className="input pl-14 pr-6 py-6 text-lg shadow-soft"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Shops */}
      <section className="section-padding">
        <div className="container-wide container-padding">
          <div className="text-center mb-16">
            <h2 className="text-display text-5xl font-bold text-[var(--color-text-primary)] mb-6">
              Featured Thrift & Antique Shops
            </h2>
            <p className="text-body text-lg text-[var(--color-text-secondary)] max-w-2xl mx-auto">
              Handpicked thrift stores and antique shops offering the finest vintage treasures
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {featuredShops.map((shop, index) => (
              <Link key={shop.id} href={`/shop/${shop.id}`} className="group">
                <div className="card group-hover:scale-[1.02] transition-all duration-500">
                  <div className="relative h-80 overflow-hidden">
                    <img
                      src={shop.image}
                      alt={shop.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <h3 className="text-heading text-2xl font-bold mb-3">
                        {shop.name}
                      </h3>
                      <p className="text-sm opacity-90 mb-4 leading-relaxed">
                        {shop.description}
                      </p>
                      <div className="flex items-center text-sm opacity-75">
                        <MapPin className="h-4 w-4 mr-2" />
                        {shop.location}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* All Shops Grid */}
      <section className="section-padding bg-[var(--color-surface)]">
        <div className="container-wide container-padding">
          <div className="flex items-center justify-between mb-16">
            <h2 className="text-display text-4xl font-bold text-[var(--color-text-primary)]">
              All Thrift & Antique Shops
            </h2>
            <Link href="/shops" className="btn-ghost">
              View All →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allShops.map((shop) => (
              <Link key={shop.id} href={`/shop/${shop.id}`} className="group">
                <div className="card">
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={shop.image}
                      alt={shop.name}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-10">
                    <h3 className="text-heading text-xl font-bold text-[var(--color-text-primary)] mb-4 group-hover:text-primary transition-colors">
                      {shop.name}
                    </h3>
                    <p className="text-body text-[var(--color-text-secondary)] mb-6 leading-relaxed">
                      {shop.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-[var(--color-text-light)]">
                        <MapPin className="h-4 w-4 mr-2" />
                        {shop.location}
                      </div>
                      <div className="flex items-center text-sm text-[var(--color-text-light)]">
                        <Heart className="h-4 w-4 mr-2" />
                        {shop.followers}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Premium Footer */}
      <footer className="bg-[var(--color-text-primary)] text-white">
        <div className="container-wide container-padding py-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-12">
            <div>
              <h3 className="text-display text-3xl font-bold mb-6 text-primary">Thrifta</h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                Connecting collectors with extraordinary vintage treasures from the world's finest thrift shops and antique stores.
              </p>
            </div>
            <div>
              <h4 className="text-caption mb-6">Company</h4>
              <ul className="space-y-4 text-gray-300">
                <li><Link href="/about" className="hover:text-white transition-colors">About Us</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/careers" className="hover:text-white transition-colors">Careers</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-caption mb-6">Support</h4>
              <ul className="space-y-4 text-gray-300">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Thrifta. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
