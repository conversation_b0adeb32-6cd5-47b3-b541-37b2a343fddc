'use client';

import { useState } from 'react';
import Link from 'next/link';
import SearchBar from '../components/SearchBar';
import HeroCarousel from '../components/HeroCarousel';
import ShopCard from '../components/ShopCard';

// Mock data for demonstration
const featuredShops = [
  {
    id: '1',
    name: 'Vintage Treasures',
    description: 'Ex-UK electronics, retro film cameras, sculpted brass lamps',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    categories: ['Electronics', 'Cameras', 'Lighting']
  },
  {
    id: '2',
    name: 'Bohemian Dreams',
    description: 'Handcrafted jewelry, vintage scarves, and artisan pottery',
    image: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    categories: ['Jewelry', 'Textiles', 'Pottery']
  },
  {
    id: '3',
    name: 'Retro Revival',
    description: 'Mid-century furniture, vinyl records, and vintage home decor',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2058&q=80',
    categories: ['Furniture', 'Music', 'Home Decor']
  }
];

const allShops = [
  {
    id: '1',
    name: 'Vintage Treasures',
    description: 'A curated collection of electronic wonders from across the pond, featuring rare cameras and unique lighting pieces.',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Electronics', 'Cameras', 'Lighting'],
    location: 'Downtown District'
  },
  {
    id: '2',
    name: 'Bohemian Dreams',
    description: 'Handcrafted treasures and vintage textiles that tell stories of distant lands and forgotten times.',
    image: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Jewelry', 'Textiles', 'Pottery'],
    location: 'Arts Quarter'
  },
  {
    id: '3',
    name: 'Retro Revival',
    description: 'Step back in time with our collection of mid-century modern pieces and nostalgic memorabilia.',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Furniture', 'Music', 'Home Decor'],
    location: 'Vintage Row'
  },
  {
    id: '4',
    name: 'Curiosity Cabinet',
    description: 'Unusual finds and conversation pieces that spark wonder and imagination.',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Antiques', 'Collectibles', 'Art'],
    location: 'Historic District'
  },
  {
    id: '5',
    name: 'Threads & Things',
    description: 'Vintage clothing and accessories that bring timeless style to modern wardrobes.',
    image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Clothing', 'Accessories', 'Shoes'],
    location: 'Fashion District'
  },
  {
    id: '6',
    name: 'Garden Relics',
    description: 'Weathered garden ornaments and outdoor treasures with character and charm.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80',
    categories: ['Garden', 'Outdoor', 'Sculptures'],
    location: 'Garden District'
  }
];

export default function Home() {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // TODO: Implement search functionality
    console.log('Searching for:', query);
  };

  const handleFilterClick = () => {
    // TODO: Implement filter functionality
    console.log('Opening filters');
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-[var(--color-background)] border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <h1 className="font-serif text-3xl font-bold text-[var(--color-primary)]">
                Thrifta
              </h1>
            </Link>
            <nav className="flex items-center space-x-6">
              <Link href="/auth/login" className="text-[var(--color-text-dark)] hover:text-[var(--color-primary)] transition-colors">
                Sign In
              </Link>
              <Link href="/auth/signup" className="btn-vintage btn-primary">
                Join Now
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Search Bar */}
      <SearchBar onSearch={handleSearch} onFilterClick={handleFilterClick} />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Carousel */}
        <section className="mb-12">
          <HeroCarousel shops={featuredShops} />
        </section>

        {/* Shop Cards Grid */}
        <section>
          <div className="flex items-center justify-between mb-8">
            <h2 className="font-serif text-3xl font-bold text-[var(--color-text-dark)]">
              Discover Shops
            </h2>
            <Link href="/shops" className="text-[var(--color-primary)] hover:opacity-80 font-medium">
              View all shops →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {allShops.map((shop) => (
              <ShopCard key={shop.id} shop={shop} />
            ))}
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-[var(--color-text-dark)] text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="font-serif text-2xl font-bold mb-4">Thrifta</h3>
              <p className="text-gray-300 mb-4">
                Discover curated vintage treasures and connect with unique shops.
                Your journey to finding hidden gems starts here.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Explore</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/shops" className="hover:text-white transition-colors">All Shops</Link></li>
                <li><Link href="/categories" className="hover:text-white transition-colors">Categories</Link></li>
                <li><Link href="/locations" className="hover:text-white transition-colors">Locations</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
            <p>&copy; 2024 Thrifta. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
